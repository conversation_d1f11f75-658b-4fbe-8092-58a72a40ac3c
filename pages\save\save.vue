<template>
	<view class="save-container">
		<!-- 顶部导航 -->
		<view class="header-section">
			<view class="back-button" @click="goBack">
				<image class="back-icon" src="/static/save-page-icons/arrow-up-icon.svg" mode="aspectFit"></image>
			</view>
			<text class="page-title">信息学</text>
		</view>

		<!-- 测试卡片列表 -->
		<view class="test-cards-section">
			<view class="test-card" v-for="(test, index) in testList" :key="index" @click="goToTest(test.id)">
				<view class="card-content">
					<view class="test-info">
						<text class="test-title">{{ test.title }}</text>
						<text class="test-description">{{ test.description }}</text>
					</view>
					<view class="test-meta">
						<view class="test-number">
							<text class="test-label">{{ test.testNumber }}</text>
							<view class="rating">
								<image class="star-icon" src="/static/save-page-icons/star-icon.svg" mode="aspectFit"></image>
								<text class="rating-text">{{ test.rating }}</text>
							</view>
							<view class="start-button" @click.stop="startTest(test.id)">
								<text class="start-text">开始</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部导航栏 -->
		<view class="tabbar-container">
			<TabBar currentPage="save" />
		</view>
	</view>
</template>

<script>
import TabBar from '@/components/TabBar.vue'

export default {
	components: {
		TabBar
	},
	data() {
		return {
			testList: [
				{
					id: 1,
					title: '信息学考试',
					description: '题目数量：60题\n考试时长：3小时',
					testNumber: '测试 1',
					rating: '5'
				},
				{
					id: 2,
					title: '信息学考试',
					description: '题目数量：60题\n考试时长：3小时',
					testNumber: '测试 2',
					rating: '5'
				},
				{
					id: 3,
					title: '信息学考试',
					description: '题目数量：60题\n考试时长：3小时',
					testNumber: '测试 3',
					rating: '5'
				},
				{
					id: 4,
					title: '信息学考试',
					description: '题目数量：60题\n考试时长：3小时',
					testNumber: '测试 4',
					rating: '5'
				}
			]
		}
	},
	onLoad() {

	},
	methods: {
		goBack() {
			uni.navigateBack();
		},
		goToTest(testId) {
			console.log('前往测试详情:', testId);
			// 这里可以添加跳转到测试详情页面的逻辑
		},
		startTest(testId) {
			console.log('开始测试:', testId);
			// 这里可以添加开始测试的逻辑
		}
	}
}
</script>

<style scoped>
.save-container {
	width: 100%;
	min-height: 100vh;
	background-color: #FFFFFF;
	display: flex;
	flex-direction: column;
	overflow-x: hidden;
}

/* 顶部导航 */
.header-section {
	display: flex;
	align-items: center;
	gap: 200rpx;
	padding: 146rpx 44rpx 32rpx;
	background-color: #FFFFFF;
	/* 确保在微信小程序中不与状态栏和胶囊按钮重叠 */
	padding-top: calc(146rpx + env(safe-area-inset-top));
	/* 为微信小程序添加额外的顶部间距 */
	/* #ifdef MP-WEIXIN */
	padding-top: calc(186rpx + env(safe-area-inset-top));
	/* #endif */
}

.back-button {
	width: 58rpx;
	height: 48rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 6rpx 12rpx;
}

.back-icon {
	width: 35rpx;
	height: 23rpx;
}

.page-title {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 32rpx;
	font-weight: 500;
	color: #000000;
	letter-spacing: 0.16rpx;
}

/* 测试卡片列表 */
.test-cards-section {
	flex: 1;
	padding: 0 44rpx;
	display: flex;
	flex-direction: column;
	gap: 32rpx;
}

.test-card {
	width: 100%;
	background-color: #FFFFFF;
	border: 2rpx solid #818181;
	border-radius: 30rpx;
	padding: 0;
	box-sizing: border-box;
	position: relative;
	height: 186rpx;
}

.card-content {
	width: 100%;
	height: 100%;
	position: relative;
	padding: 20rpx 28rpx;
	box-sizing: border-box;
}

.test-info {
	position: absolute;
	top: 20rpx;
	left: 28rpx;
	width: 416rpx;
}

.test-title {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 30rpx;
	font-weight: 700;
	color: #000000;
	line-height: 1.2;
	display: block;
	margin-bottom: 10rpx;
}

.test-description {
	font-family: 'Montserrat', sans-serif;
	font-size: 24rpx;
	font-weight: 400;
	color: #818181;
	line-height: 2.5;
}

.test-meta {
	position: absolute;
	top: 16rpx;
	right: 28rpx;
	display: flex;
	flex-direction: column;
	gap: 22rpx;
}

.test-number {
	display: flex;
	flex-direction: column;
	gap: 22rpx;
	align-items: flex-end;
}

.test-label {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 30rpx;
	font-weight: 500;
	color: #F2282D;
	line-height: 1.26;
}

.rating {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.star-icon {
	width: 28rpx;
	height: 26rpx;
}

.rating-text {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 28rpx;
	font-weight: 500;
	color: #000000;
	line-height: 1.26;
}

.start-button {
	background-color: #F2282D;
	border-radius: 100rpx;
	padding: 6rpx 12rpx;
	height: 44rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 6rpx;
}

.start-text {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 24rpx;
	font-weight: 500;
	color: #FFFFFF;
	line-height: 1.26;
}

/* TabBar容器 */
.tabbar-container {
	background-color: #FFFFFF;
	padding: 0 34rpx 34rpx;
	display: flex;
	justify-content: center;
	flex-shrink: 0;
}

/* uniapp使用rpx单位自动适配不同屏幕尺寸，无需媒体查询 */
</style>
